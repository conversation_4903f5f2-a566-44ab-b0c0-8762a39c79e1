import { Request, Response, NextFunction } from "express";

// Simple in-memory rate limiter (for study purposes)
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

// Cleanup expired entries every 5 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 5 * 60 * 1000);

export interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string; // Custom error message
  skipSuccessfulRequests?: boolean; // Don't count successful requests
}

export const createRateLimiter = (options: RateLimitOptions) => {
  const {
    windowMs,
    maxRequests,
    message = "Too many requests, please try again later.",
    skipSuccessfulRequests = false,
  } = options;

  return (req: Request, res: Response, next: NextFunction) => {
    // Get client IP (considering proxy headers for development)
    const clientIP =
      req.ip ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      (req.connection as any)?.socket?.remoteAddress ||
      "unknown";

    const key = `${clientIP}:${req.route?.path || req.path}`;
    const now = Date.now();
    const resetTime = now + windowMs;

    let entry = rateLimitStore.get(key);

    // If no entry exists or the window has expired, create a new one
    if (!entry || now > entry.resetTime) {
      entry = { count: 0, resetTime };
      rateLimitStore.set(key, entry);
    }

    // Check if limit is exceeded
    if (entry.count >= maxRequests) {
      const timeUntilReset = Math.ceil((entry.resetTime - now) / 1000);

      res.status(429).json({
        message,
        retryAfter: timeUntilReset,
        limit: maxRequests,
        remaining: 0,
      });
      return;
    }

    // Increment counter (unless we're skipping successful requests)
    if (!skipSuccessfulRequests) {
      entry.count++;
    } else {
      // We'll increment after the response if it's not successful
      const originalSend = res.send;
      res.send = function (data) {
        if (res.statusCode >= 400) {
          entry!.count++;
        }
        return originalSend.call(this, data);
      };
    }

    // Add rate limit headers
    res.set({
      "X-RateLimit-Limit": maxRequests.toString(),
      "X-RateLimit-Remaining": Math.max(
        0,
        maxRequests - entry.count
      ).toString(),
      "X-RateLimit-Reset": new Date(entry.resetTime).toISOString(),
    });

    next();
  };
};

// Predefined rate limiters for common use cases
export const authRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 login attempts per 15 minutes
  message: "Too many authentication attempts, please try again in 15 minutes.",
  skipSuccessfulRequests: true, // Only count failed attempts
});

export const generalRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // 100 requests per 15 minutes
  message: "Too many requests, please try again later.",
});

export const messageRateLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  maxRequests: 30, // 30 messages per minute
  message: "Too many messages, please slow down.",
});

// Strict rate limiter for sensitive operations
export const strictRateLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxRequests: 30, // 3 attempts per hour
  message: "Too many attempts, please try again in 1 hour.",
  skipSuccessfulRequests: true,
});
