import jwt from "jsonwebtoken";
import { Response } from "express";

// Simple in-memory store for refresh tokens (for study purposes)
const refreshTokenStore = new Map<string, string>();

export const generateTokens = (
  userId: string,
  res: Response
): { accessToken: string; refreshToken: string } => {
  // Access token - short lived (15 minutes)
  const accessToken = jwt.sign({ userId }, process.env.JWT_SECRET!, {
    expiresIn: "15m",
  });

  // Refresh token - longer lived (7 days)
  const refreshToken = jwt.sign(
    { userId, type: "refresh" },
    process.env.JWT_SECRET!,
    {
      expiresIn: "7d",
    }
  );

  // Store refresh token in memory (in production, use Redis or database)
  refreshTokenStore.set(userId, refreshToken);

  // Set access token as httpOnly cookie
  res.cookie("jwt", accessToken, {
    maxAge: 15 * 60 * 1000, // 15 minutes
    httpOnly: true,
    sameSite: "strict",
    secure: process.env.NODE_ENV !== "development",
  });

  // Set refresh token as httpOnly cookie
  res.cookie("refreshToken", refreshToken, {
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    httpOnly: true,
    sameSite: "strict",
    secure: process.env.NODE_ENV !== "development",
  });

  return { accessToken, refreshToken };
};

// Legacy function for backward compatibility
export const generateToken = (userId: string, res: Response): string => {
  const { accessToken } = generateTokens(userId, res);
  return accessToken;
};

export const verifyRefreshToken = (
  refreshToken: string,
  userId: string
): boolean => {
  try {
    const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET!) as any;
    const storedToken = refreshTokenStore.get(userId);

    return (
      decoded.userId === userId &&
      decoded.type === "refresh" &&
      storedToken === refreshToken
    );
  } catch {
    return false;
  }
};

export const revokeRefreshToken = (userId: string): void => {
  refreshTokenStore.delete(userId);
};

export const cleanupExpiredTokens = (): void => {
  // Simple cleanup - in production, you'd want a more sophisticated approach
  for (const [userId, token] of refreshTokenStore.entries()) {
    try {
      jwt.verify(token, process.env.JWT_SECRET!);
    } catch {
      refreshTokenStore.delete(userId);
    }
  }
};
