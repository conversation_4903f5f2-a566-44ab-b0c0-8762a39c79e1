{"name": "realtime-chat-api", "version": "1.0.0", "main": "./dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc && nodemon dist/index.js", "dev:watch": "tsc --watch", "clean": "rm -rf dist", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "mateus-lopes", "type": "module", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.21", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.10", "typescript": "^5.8.3"}}